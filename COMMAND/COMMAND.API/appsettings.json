{"ConnectionStrings": {"ConnectionStrings": "Server=***************;Database=Beautify;Uid=sa;Pwd=*********;Trust Server Certificate=True;", "Redis": "***************:6379,password=*********,abortConnect=false"}, "JwtOption": {"Issuer": "http://**************:0000", "Audience": "http://**************:000", "SecretKey": "IRanUIwukUBzSauFsZnr7AjV7XS96sun", "ExpireMin": 5}, "MasstransitConfiguration": {"Host": "***************", "VHost": "myHost", "Port": 5672, "UserName": "sa", "Password": "*********"}, "MessageBusOptions": {"retryLimit": 3, "initialInterval": "00:00:05", "intervalIncrement": "00:00:10"}, "CloudinaryOptions": {"CloudName": "dvadlh7ah", "ApiKey": "611119568732129", "ApiSecret": "lvyo8r9YICLxWDnZq4UB4LeAVhE"}, "MailOption": {"Mail": "<EMAIL>", "DisplayName": "Beautify System", "Password": "awonvixcvsnpgjxz", "Host": "smtp.gmail.com", "Port": 587}, "SqlServerRetryOptions": {"MaxRetryCount": 5, "MaxRetryDelay": "00:00:05", "ErrorNumbersToAdd": []}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"Theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console"}}, {"Name": "File", "Args": {"path": "logs/log-.txt", "rollingInterval": "Day", "shared": true}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "Domain": "localhost:3000"}